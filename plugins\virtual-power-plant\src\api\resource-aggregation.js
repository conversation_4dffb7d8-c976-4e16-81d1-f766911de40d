/**
 * 资源聚合模块 API 接口
 *
 * 功能说明：
 * - 机组创建与资源绑定
 * - 机组管理（查看、修改、删除）
 * - 资源绑定关系管理
 * - 统计与展示功能
 */
import fetch from "eem-base/utils/fetch";

const prefix = "/vpp/api/v1/resource-aggregation";

// ==================== 机组管理 ====================

/**
 * 创建新机组
 * @param {Object} data - 机组数据
 * @param {string} data.unitName - 机组名称
 * @param {number} data.unitType - 机组类型 (0:全部, 1:需求响应, 2:调峰, 3:调频, 4:现货, 5:中长期)
 * @param {Array<string>} data.resourceIds - 绑定的资源ID列表
 * @returns {Promise} 请求Promise
 */
export function createUnit(data) {
  return fetch({
    url: `${prefix}/unit`,
    method: "POST",
    data
  });
}

/**
 * 删除机组
 * @param {number|string} unitId - 机组ID
 * @returns {Promise} 请求Promise
 */
export function deleteUnit(unitId) {
  return fetch({
    url: `${prefix}/unit/${unitId}`,
    method: "DELETE"
  });
}

/**
 * 更新机组信息
 * @param {number|string} unitId - 机组ID
 * @param {Object} data - 更新数据
 * @param {string} data.unitName - 机组名称
 * @param {Array<string>} data.resourceIds - 绑定的资源ID列表
 * @returns {Promise} 请求Promise
 */
export function updateUnit(unitId, data) {
  return fetch({
    url: `${prefix}/unit/${unitId}`,
    method: "POST",
    data
  });
}

/**
 * 获取机组绑定的资源列表
 * @param {Object} data - 查询参数
 * @param {number} [data.page] - 页码
 * @param {number} [data.pageSize] - 每页数量
 * @returns {Promise} 请求Promise
 */
export function getUnitResources(data) {
  return fetch({
    url: `${prefix}/unit/bound-resources`,
    method: "POST",
    data
  });
}

/**
 * 获取机组列表
 * @param {Object} data - 查询参数
 * @param {string} [data.unitType] - 机组类型过滤
 * @param {string} [data.unitName] - 机组名称模糊搜索
 * @param {number} [data.page] - 页码
 * @param {number} [data.pageSize] - 每页数量
 * @returns {Promise} 请求Promise
 */
export function listUnits(data) {
  return fetch({
    url: `${prefix}/unit/list`,
    method: "POST",
    data
  });
}

// ==================== 资源管理 ====================

/**
 * 获取可绑定的资源列表
 * @param {Object} data - 查询参数
 * @param {number} data.unitType - 机组类型（必填）
 * @param {number|string} [data.unitId] - 机组ID（编辑时传入）
 * @param {number} [data.page] - 页码
 * @param {number} [data.pageSize] - 每页数量
 * @param {string} data.district - 资源所属区域（必填）
 * @param {string} [data.searchKeyword] - 搜索关键词，支持模糊查找
 * @returns {Promise} 请求Promise
 */
export function getAvailableResources(data) {
  return fetch({
    url: `${prefix}/unit/available-resources`,
    method: "POST",
    data
  });
}

/**
 * 从机组解绑单个资源
 * @param {number|string} unitId - 机组ID
 * @param {string} resourceId - 资源ID
 * @returns {Promise} 请求Promise
 */
export function unbindResource(unitId, resourceId) {
  return fetch({
    url: `${prefix}/unit/${unitId}/unbind-resource/${resourceId}`,
    method: "DELETE"
  });
}

/**
 * 获取资源区域枚举值
 * @returns {Promise} 请求Promise
 */
export function getDistricts() {
  return fetch({
    url: `${prefix}/resources-districts`,
    method: "GET"
  });
}