# AddUnitDialog.vue 分页选中状态保持功能修改说明

## 修改概述
解决了AddUnitDialog.vue组件中分页切换时选中状态丢失的问题，实现了跨页面保持选中状态的功能。

## 主要修改内容

### 1. 导入新的API
```javascript
import {
  getAvailableResources,
  getDistricts,
  getUnitResources  // 新增：用于获取已绑定资源
} from "@/api/resource-aggregation";
```

### 2. 修改initDialog方法
- 改为异步方法，支持在编辑模式下加载已绑定资源
- 编辑模式下调用`loadBoundResources()`方法

### 3. 新增loadBoundResources方法
- 在编辑模式下获取机组已绑定的所有资源
- 将已绑定资源设置为初始选中状态

### 4. 修改loadAvailableResources方法
- 在数据加载完成后调用`restoreTableSelection()`恢复表格选中状态

### 5. 新增restoreTableSelection方法
- 根据selectedResources数组恢复当前页面的表格选中状态
- 使用ElementUI的toggleRowSelection方法设置选中状态

### 6. 新增isResourceSelected辅助方法
- 检查指定资源是否已被选中

### 7. 重写handleSelectionChange方法
- 改为智能合并选中状态的逻辑
- 先移除当前页面的所有资源，再添加新选中的资源
- 保持其他页面的选中状态不变

### 8. 优化对话框关闭逻辑
- 新增resetDialogState方法统一重置对话框状态
- 修改handleClose和handleCancel方法调用重置方法

### 9. 修改handleConfirm方法
- 移除确认后清空selectedResources的逻辑，避免影响后续操作

## 功能特点

1. **跨页面选中状态保持**：用户在不同页面选中的资源会被保留
2. **编辑模式支持**：编辑模式下正确显示已绑定的资源选中状态
3. **智能状态管理**：分页切换时自动恢复表格选中状态显示
4. **性能优化**：只在必要时更新选中状态，避免不必要的操作

## 使用场景

- 新增模式：用户可以跨页面选择资源，选中状态会被保持
- 编辑模式：自动显示已绑定的资源选中状态，用户可以修改选择

## 技术实现

- 使用Vue2 + ElementUI的最佳实践
- 遵循现有代码风格和架构
- 保持与现有API接口的兼容性
- 使用$nextTick确保DOM更新后再操作表格选中状态
