# AddUnitDialog.vue 功能测试验证

## 测试场景

### 1. 新增模式测试
**测试步骤：**
1. 点击"新增"按钮打开对话框
2. 选择机组类型，等待资源列表加载
3. 在第一页选中几个资源
4. 切换到第二页，选中几个资源
5. 再切换回第一页

**预期结果：**
- 第一页之前选中的资源应该仍然保持选中状态
- 表格中的复选框应该正确显示选中状态

### 2. 编辑模式测试
**测试步骤：**
1. 点击某个机组的"编辑"按钮
2. 等待对话框打开和数据加载
3. 查看资源列表中已绑定资源的选中状态
4. 切换分页查看其他页面的资源
5. 修改选中状态后切换页面

**预期结果：**
- 已绑定的资源应该显示为选中状态
- 分页切换时选中状态应该保持
- 可以正常修改选中状态

### 3. 分页切换测试
**测试步骤：**
1. 在资源列表第一页选中部分资源
2. 切换到第二页选中部分资源
3. 切换到第三页选中部分资源
4. 依次切换回第二页、第一页

**预期结果：**
- 每个页面的选中状态都应该被正确保持
- 表格复选框状态与实际选中状态一致

### 4. 搜索和筛选测试
**测试步骤：**
1. 选中部分资源
2. 使用搜索功能筛选资源
3. 在筛选结果中修改选中状态
4. 清空搜索条件

**预期结果：**
- 搜索前的选中状态应该保持
- 搜索后的修改应该正确应用

### 5. 区域筛选测试
**测试步骤：**
1. 在当前区域选中部分资源
2. 切换到其他区域
3. 在新区域选中部分资源
4. 切换回原区域

**预期结果：**
- 不同区域的选中状态应该独立管理
- 切换区域时选中状态应该正确恢复

## 关键验证点

### 1. 数据一致性
- selectedResources数组中的数据与表格显示一致
- 提交时的数据包含所有选中的资源

### 2. 性能表现
- 分页切换响应速度正常
- 选中状态恢复不会造成明显延迟

### 3. 用户体验
- 选中状态变化符合用户预期
- 没有意外的状态丢失或错误选中

### 4. 边界情况
- 空数据时的处理
- 网络错误时的处理
- 大量数据时的性能

## 调试信息

可以在浏览器控制台查看以下信息：
- `this.selectedResources` - 当前选中的资源数组
- `this.tableData` - 当前页面的表格数据
- 网络请求的响应数据

## 常见问题排查

1. **选中状态不保持**
   - 检查handleSelectionChange方法是否正确执行
   - 确认selectedResources数组更新正确

2. **表格显示不正确**
   - 检查restoreTableSelection方法是否被调用
   - 确认$nextTick时机是否正确

3. **编辑模式初始化失败**
   - 检查loadBoundResources方法是否正确执行
   - 确认API返回数据格式正确
